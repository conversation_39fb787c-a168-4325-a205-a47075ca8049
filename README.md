# sync_bank_files_prod

银行文件同步项目，用于管理和同步银行SFTP用户及其相关脚本。

## 项目说明

本项目主要用于：
1. 管理银行SFTP用户的创建和维护
2. 部署银行文件处理脚本
3. 自动化同步银行文件

## 主要功能

### SFTP用户管理
- 创建/修改SFTP用户
- 设置用户权限和目录
- 配置SSH密钥认证
- 通过企业微信通知用户账号信息

### 银行脚本部署
- 自动部署Python处理脚本
- 自动部署Shell处理脚本
- 脚本权限管理

### 用户创建流程
1. 在 `vault.vars.yml` 中添加用户密码配置
2. 在 `sftp_user.gitlab-ci.yml` 中添加用户任务配置
3. 设置用户名（如：`dts075`）和银行目录名（如：`sss075`）
4. 执行GitLab CI/CD流水线中的用户创建任务
5. 系统自动创建用户、设置权限并配置SFTP访问
6. 用户信息通过企业微信通知相关人员

### 用户修改说明
默认情况下，系统不允许修改已存在的用户或目录，这是为了防止意外覆盖现有配置。如需修改现有用户：

1. 在执行任务时需要传递 `to_modify=true` 参数
2. 修改模式下，系统将：
   - 跳过用户存在检查
   - 跳过目录存在检查
   - 更新用户配置而非创建新用户

## 项目结构

```
.
├── deploy/
│   ├── roles/
│   │   ├── copy_bank_script/    # 银行脚本部署角色
│   │   └── sftp_users/          # SFTP用户管理角色
│   ├── copy_bank_script_playbook.yml
│   └── sftp_users_playbook.yml
├── vault.vars.yml               # 加密的敏感配置信息
├── sftp_bank_script.gitlab-ci.yml
├── sftp_user.gitlab-ci.yml
└── .gitlab-ci.yml
```

## 使用说明

### SFTP用户创建
1. 在 `vault.vars.yml` 中添加用户密码配置
2. 执行GitLab CI/CD流水线中的用户创建任务
3. 用户信息将通过企业微信通知

### 银行脚本部署
1. 将脚本文件放置在对应目录：
   - Python脚本: `deploy/roles/copy_bank_script/templates/py_script/`
   - Shell脚本: `deploy/roles/copy_bank_script/templates/sh_script/`
2. 执行GitLab CI/CD流水线中的脚本部署任务

## 部署环境

- 生产环境服务器: SFTP-DATA
- 部署路径: /opt/scripts
- SFTP用户目录: /data/banks/uploads/{bankname}

## CI/CD配置

项目使用GitLab CI/CD进行自动化部署，主要阶段：
- sftp_users_test: SFTP用户测试环境部署
- sftp_users: SFTP用户生产环境部署
- copy_script_to_prod_host: 银行脚本部署

## 注意事项

1. 所有敏感信息都存储在加密的 `vault.vars.yml` 文件中
2. 生产环境部署需要手动触发
3. 用户创建后会自动通过企业微信发送通知
4. 脚本部署前需确保文件权限正确
5. 默认不允许修改已存在的用户，需要使用 `to_modify=true` 参数

## 维护人员

- @jinglin.song
- @huangdongxing

## 相关链接

- [项目仓库](http://*************/ev_devops_prod/sync_bank_files_prod)
- [流水线状态](http://*************/ev_devops_prod/sync_bank_files_prod/-/pipelines)
