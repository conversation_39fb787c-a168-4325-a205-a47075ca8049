#!/bin/bash
# 密码生成器
# 生成12位包含数字大小写字母的密码及其加密版本
# 用法: ./generate_password.sh <用户名>

# 固定密码长度为12位
PASSWORD_LENGTH=12

# 显示用法
show_usage() {
    echo "用法: $0 <用户名>"
    echo ""
    echo "参数:"
    echo "  用户名        要生成密码的用户名"
    echo ""
    echo "示例:"
    echo "  $0 dts053"
    echo ""
    echo "说明: 自动生成12位密码（包含数字、大小写字母）"
    exit 0
}

# 生成随机密码
generate_password() {
    local length=$1
    # 生成包含大小写字母和数字的随机密码
    tr -dc 'A-Za-z0-9' < /dev/urandom | head -c "$length"
}

# 加密密码
encrypt_password() {
    local plain_password=$1
    
    # 检查是否有python3
    if command -v python3 &> /dev/null; then
        python3 -c "import crypt; print(crypt.crypt('$plain_password'))"
    elif command -v python &> /dev/null; then
        python -c "import crypt; print(crypt.crypt('$plain_password'))"
    else
        # 如果没有Python，使用openssl作为替代
        echo "警告：Python不可用，使用SHA256哈希作为替代" >&2
        echo -n "$plain_password" | openssl dgst -sha256 | awk '{print "$sha256$" $2}'
    fi
}

# 解析命令行参数
parse_args() {
    if [ $# -eq 0 ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_usage
    fi

    if [ $# -ne 1 ]; then
        echo "错误：请提供用户名"
        show_usage
    fi

    USERNAME="$1"
}

# 主函数
main() {
    parse_args "$@"

    # 生成密码
    PLAIN_PASSWORD=$(generate_password "$PASSWORD_LENGTH")
    ENCRYPTED_PASSWORD=$(encrypt_password "$PLAIN_PASSWORD")

    # 直接输出vault.vars.yml格式
    echo "  $USERNAME: \"$ENCRYPTED_PASSWORD\""
    echo "  ${USERNAME}_plain: \"$PLAIN_PASSWORD\""
}

# 运行主函数
main "$@"
