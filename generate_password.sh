#!/bin/bash
# 简单的密码生成器 Bash版本
# 生成12位包含数字大小写字母的密码及其加密版本
# 用法: ./generate_password.sh [用户名] [密码长度] [数量]

# 默认参数
USERNAME=""
PASSWORD_LENGTH=12
COUNT=1

# 显示用法
show_usage() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -u, --username <用户名>    用户名（可选）"
    echo "  -l, --length <长度>        密码长度 (默认: 12)"
    echo "  -c, --count <数量>         生成密码的数量 (默认: 1)"
    echo "  -h, --help                 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0"
    echo "  $0 -u testuser"
    echo "  $0 -l 16 -c 3"
    echo "  $0 --username testuser --length 16"
    exit 0
}

# 生成随机密码
generate_password() {
    local length=$1
    # 生成包含大小写字母和数字的随机密码
    tr -dc 'A-Za-z0-9' < /dev/urandom | head -c "$length"
}

# 加密密码
encrypt_password() {
    local plain_password=$1
    
    # 检查是否有python3
    if command -v python3 &> /dev/null; then
        python3 -c "import crypt; print(crypt.crypt('$plain_password', crypt.mksalt(crypt.METHOD_SHA512)))"
    elif command -v python &> /dev/null; then
        python -c "import crypt; print(crypt.crypt('$plain_password', crypt.mksalt(crypt.METHOD_SHA512)))"
    else
        # 如果没有Python，使用openssl作为替代
        echo "警告：Python不可用，使用SHA256哈希作为替代" >&2
        echo -n "$plain_password" | openssl dgst -sha256 | awk '{print "$sha256$" $2}'
    fi
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -u|--username)
                USERNAME="$2"
                shift 2
                ;;
            -l|--length)
                PASSWORD_LENGTH="$2"
                shift 2
                ;;
            -c|--count)
                COUNT="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                ;;
            *)
                # 如果是第一个位置参数，当作用户名
                if [ -z "$USERNAME" ] && [[ ! "$1" =~ ^- ]]; then
                    USERNAME="$1"
                    shift
                else
                    echo "未知选项: $1"
                    show_usage
                fi
                ;;
        esac
    done
}

# 主函数
main() {
    parse_args "$@"
    
    echo "============================================================"
    echo "密码生成器"
    echo "============================================================"
    
    for ((i=1; i<=COUNT; i++)); do
        if [ "$COUNT" -gt 1 ]; then
            echo ""
            echo "--- 密码 $i ---"
        fi
        
        # 生成密码
        PLAIN_PASSWORD=$(generate_password "$PASSWORD_LENGTH")
        ENCRYPTED_PASSWORD=$(encrypt_password "$PLAIN_PASSWORD")
        
        if [ -n "$USERNAME" ]; then
            echo "用户名: $USERNAME"
        fi
        echo "明文密码: $PLAIN_PASSWORD"
        echo "加密密码: $ENCRYPTED_PASSWORD"
        
        if [ -n "$USERNAME" ]; then
            echo ""
            echo "用于vault.vars.yml的格式:"
            echo "  $USERNAME: \"$ENCRYPTED_PASSWORD\""
            echo "  ${USERNAME}_plain: \"$PLAIN_PASSWORD\""
        fi
        
        echo "------------------------------------------------------------"
    done
}

# 运行主函数
main "$@"
