import os
import pandas as pd
import requests
import time
import datetime

def get_mtime_recored(file_path):
    try:
        with open(file_path, 'r') as file:
            content = file.read()
            if content:
                return float(content)
            else:
                return 0.0
    except FileNotFoundError:
        return 0.0


def get_newly_flle(timestamp,folder_path):
    file_list = []

    # 遍历文件夹下的所有文件和子文件夹
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            
            (_, ext) = os.path.splitext(file)
            if ext != ".xls":
                continue

            # 获取文件的修改时间
            mtime = os.path.getmtime(file_path)

            # 比较文件的修改时间和给定的时间戳
            #print(mtime)
            if mtime > float(timestamp):
                print(mtime)
                print(file_path)
                file_list.append(file_path)

    return file_list


def extract_dates_from_filenames(file_list):
    dates = set()
    for filename in file_list:
        parts = filename.split('_')  # 通过下划线分割文件名
        last_part = parts[-1]  # 获取最后一个元素作为日期部分
        date = last_part.split('.')[0]  # 去除扩展名部分
        dates.add(date)

    return dates


def get_files_with_date(folder_path, dateStr):
    file_list = []

    # 遍历文件夹下的所有文件和子文件夹
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if dateStr in file:
                file_path = os.path.join(root, file)
                file_list.append(file_path)
    return file_list

def get_max_modified_time(path):
    max_modified_time = None

    for root, dirs, files in os.walk(path):
        for file in files:
            if file.endswith('.xls'):
                file_path = os.path.join(root, file)
                file_modified_time = os.path.getmtime(file_path)
                if max_modified_time is None or file_modified_time > max_modified_time:
                    max_modified_time = file_modified_time

    return max_modified_time

def process_file(file_path, url):
    # 根据文件路径处理文件，并返回处理结果的 DataFrame
    # 这里假设你已经实现了一个函数来处理文件并返回 DataFrame
    # 这里的示例代码只是简单地创建一个 DataFrame

    df1 = pd.read_excel(file_path, sheet_name='Summary')
    # 读取 Excel 文件，指定要读取的工作表和需要跳过的行数
    df2 = pd.read_excel(file_path, sheet_name='Valuation', header=None, nrows=11, usecols=[0, 1])

    # 选择前2列的数据，并指定第一列为表头
    # 设置表头
    df2 = df2.T
    df2.set_axis(df2.iloc[0], axis='columns', inplace=True)
    df2 = df2[1:]
    df2['Account Code'] = df1['Account Code'].iloc[0]



    df3 = pd.read_excel(file_path, sheet_name='CashMovement (USD)')


    # 准备进行多账号预警处理
    df4 = pd.read_excel(file_path, sheet_name='Valuation')
    # 去除前面几行
    df4 = df4.iloc[10:]
    # 去除后面几行
    df4 = df4.iloc[:-3]
    # 重新设置表头
    df4.columns = df4.iloc[0]  # 将第一行设置为表头
    df4 = df4[1:]  # 删除第一行，即之前被当作数据的行

    # 特殊处理
    accounts_of_df3 = set(df3['Account Code'])
    #print(accounts_of_df3)
    if 'WI01480' in accounts_of_df3:
        accounts_of_df3.remove('WI01480')
        #print(accounts_of_df3)

    accounts = set(df1['Account Code']) | set(df3['Account Code']) | set(df4['Account Code'])
    print(accounts)
    if 'WI01480' in accounts:
        accounts.remove('WI01480')
        print(accounts)

    if len(accounts)>1:
        str_accounts = ','.join(accounts)
        msg = f'CICC文件： {file_path} 存在多个账号:{str_accounts}'
        send_msg_text(url, msg)
    return df1,df2,df3

def merge_dataframes(dataframes):
    # 根据类型合并 DataFrame
    merged_df = pd.concat(dataframes)
    return merged_df

def send_msg_text(url, content):
    # 构造消息内容
    message = {
        "msgtype": "text",
        "text": {
            "content": content
        }
    }
    requests.post(url=url, json=message)


if __name__ == '__main__':

    folder_path = '/data/banks/uploads/fcs_hk_hsbc_cicc/cicc'
    #folder_path = '/home/<USER>/cicc/data'
    dir_path = '/data/banks/bankassets/fcs_hk_cicc_mn/cicc/'
    record_file_path = '/data/banks/uploads/fcs_hk_hsbc_cicc/cicc/mtime_record.txt'
    url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=41a0b7ea-84cf-4e1d-99f8-aba47ca64dc9'

    # 获取记录文件中的m_time
    mtime_recored = get_mtime_recored("mtime_record.txt")

    # 获取文件列表
    files = get_newly_flle(mtime_recored,folder_path)

    max_mtime = get_max_modified_time(folder_path)

    print(max_mtime)

    # 获取所有的待处理日期，调用函数提取日期并进行去重
    dates = extract_dates_from_filenames(files)

    # 每个结算日数据的处理
    for dateStr in dates:
        print(dateStr)
        # 对每个日期单独处理,获取文件列表
        dateFiles =  get_files_with_date(folder_path,dateStr)

        df1s = []
        df2s = []
        df3s = []
        for file_path in dateFiles:
           df1,df2,df3 = process_file(file_path, url)
           df1s.append(df1)
           df2s.append(df2)
           df3s.append(df3)

        merged_df1 = merge_dataframes(df1s)
        merged_df2 = merge_dataframes(df2s)
        merged_df3 = merge_dataframes(df3s)

        merged_df1.to_csv(dir_path+'Summary_'+dateStr+'.csv', index=False)
        merged_df2.to_csv(dir_path+'Valuation_'+dateStr+'.csv', index=False)
        merged_df3.to_csv(dir_path+'CashMovement_'+dateStr+'.csv', index=False)

    # 将最大修改时间写入记录文件

    with open(record_file_path, 'w') as record_file:
        record_file.write(str(max_mtime))

    #print("finished")
    timestamp = time.time()
    formatted_time = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
    print("finished : ", formatted_time)
