#! /bin/env python3

import sys
import argparse
import os
from shutil import copy2
from datetime import datetime

def readable_dir(prospective_dir):
  if not os.path.isdir(prospective_dir):
    raise Exception("readable_dir:{0} is not a valid path".format(prospective_dir))
  if os.access(prospective_dir, os.R_OK):
    return prospective_dir
  else:
    raise Exception("readable_dir:{0} is not a readable dir".format(prospective_dir))

def valid_date(s):
    try:
        return datetime.strptime(s, "%Y%m%d").date()
    except ValueError:
        msg = "not a valid date: {0!r}".format(s)
        raise argparse.ArgumentTypeError(msg)

def getClearingDateFromFileName(filename):
    dateIndex = 3 if filename.startswith('positions') or filename.startswith('historictransaction') else 2
    print(filename)
    return datetime.strptime(filename.split('_')[dateIndex][:8], "%Y%m%d").date()

def convertFileName(isHK, isSG, name):
  # HK
  # positions_A_HKP2986176_20220525091342631_352.csv.gpg --> Positions_3209747W_CSV_25052022_25052022_90352.CSV.gpg
  # positions_B_HKP2986176_20220524125255904_351.csv.gpg --> Positions_3209747W_CSV_24052022_24052022_91351.CSV.gpg
  # transaction_HKP2986176_20220524085331609_356.csv.gpg --> Transactions_3209747W_CSV_24052022_24052022_99356.XML.gpg

  # SG
  # transaction_SGP7106389_20220525091558280_357.csv.gpg --> Transactions_2971792J_CSV_25052022_25052022_99357.CSV.gpg 

  filename = os.path.basename(name)
  decryptedFilename, gpg = os.path.splitext(name)
  _, ext = os.path.splitext(decryptedFilename)
  ext = ext[1:]

  accStr = '3209747W' if isHK else '2971792J'
  dateStr = getClearingDateFromFileName(name).strftime('%d%m%Y')
  if name.startswith('positions_'):
    fileIdPrefix = '0' if filename.split('_')[1] == 'A' else '1'
    fileId = filename.split('_')[4].split('.')[0]
    return f"Positions_{accStr}_{ext.upper()}_{dateStr}_{dateStr}_9{fileIdPrefix}{fileId}.{ext.upper()}.gpg"
  elif name.startswith('transaction_')  or name.startswith('historictransaction'):
    fileId = filename.split('_')[3].split('.')[0]
    return f"Transactions_{accStr}_{ext.upper()}_{dateStr}_{dateStr}_99{fileId}.{ext.upper()}.gpg"

def main(argv):
  parser = argparse.ArgumentParser(description='Move jb files in new format to the legacy fomrat in the legacy folder.')
  parser.add_argument('-s', '--srcDir', type=readable_dir)
  parser.add_argument('-d', '--destDir', type=readable_dir)
  parser.add_argument('--hk', action='store_true')
  parser.add_argument('--sg', action='store_true')
  parser.add_argument('--since', type=valid_date, default=datetime.now().date())
  parser.add_argument('--dry-run', action='store_true')

  args = vars(parser.parse_args())
  print(args)

  srcDir = args['srcDir']
  destDir = args['destDir']
  isHK = args['hk']
  isSG = args['sg']
  isDryRun = args['dry_run']
  since = args['since']

  if (isHK and isSG) or (not isHK and not isSG):
    print('You must set exactly one of --hk or --sg.')
    sys.exit()

  # loop all .gpg files in {srcDir}, copy over to {destDir} if newer.
  fileCount = 0
  for f in os.listdir(srcDir):
    if not os.path.isfile(os.path.join(srcDir, f)):
      continue

    file_name, ext = os.path.splitext(f)
    if not ext == '.gpg':
      continue
    if 'historicpositions' in file_name:
      continue

    date = getClearingDateFromFileName(f)
    if (date < since):
      continue
    
    s = os.path.join(srcDir, f)
    d = os.path.join(destDir, convertFileName(isHK, isSG, f))

    if (not os.path.exists(d) or os.path.getmtime(s) > os.path.getmtime(d)):
      fileCount += 1
      print(f"copying {s} to {d}...")
      if (not isDryRun):
        copy2(s, d)
        print(f"copy completed.")

  print("")
  print(f"Copied {fileCount} files in total.")

if __name__ == "__main__":
  main(sys.argv)
