#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/7/31 12:21
# <AUTHOR> yuan
# @File    : zipBos.py
# @Software: PyCharm
# @Function: 前置处理redbeacon bos文件,方便后续脚本统一处理

import os
import re
import zipfile

src_dir = '/data/banks/uploads/redbeacon_bos_sg/bos_sg'
tar_dir = '/data/banks/bankassets/bos_sg_redbeacon'



# 获取文件列表，并过滤出以 .csv 结尾的文件
csv_files = [file for file in os.listdir(src_dir) if file.endswith('.csv')]

# 将文件按日期进行分组
date_groups = {}
for file in csv_files:
    # 使用正则表达式提取日期
    date_match = re.search(r'\d{4}-\d{2}-\d{2}', file)
    if date_match:
        date = date_match.group()
        if date in date_groups:
            date_groups[date].append(file)
        else:
            date_groups[date] = [file]

# 压缩文件并重命名
for date, files in date_groups.items():
    archive_name = f"IAM_Daily_Feed_{date}.zip"
    archive_path = os.path.join(tar_dir, archive_name)
    # 检查对应日期的压缩包是否已存在，如果存在则跳过处理
    if os.path.exists(archive_path):
        print(f"对应日期的压缩包已存在，跳过处理：{archive_name}")
        continue
    # 创建并打开 ZIP 压缩包
    with zipfile.ZipFile(archive_path, "w") as zip_file:
        # 将每个文件逐个添加到压缩包中
        for file in files:
            # 添加文件到压缩包中
            file_path = os.path.join(src_dir, file)
            arcname = os.path.basename(file)  # 压缩包内的文件名为原始文件名
            zip_file.write(file_path, arcname=arcname)

    # 打印完成信息
    print(f"文件压缩完成，压缩包名称为：{archive_name}")
print("文件按日期分组并压缩完成！")
