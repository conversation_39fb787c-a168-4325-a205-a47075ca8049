# cs_reabeacon给过来的是带.的隐藏文件,这个在之前的driver模块中直接忽略掉了,而且隐藏的zip文件解压之后还是zip文件,和之前的有区别,这个脚本做一个归一化处理
# 功能：将指定路径下的隐藏zip文件,解压到当前目录

# 定义文件夹列表(以后可能也会有其他的cs出现这种情况)
import os
import time
from zipfile import ZipFile

folder_list = ['/data/banks/bankassets/red_beacon_cs_sg']


if __name__ == '__main__':

    # 获取当前的timestamp
    current_timestamp = int(time.time())
    for foler in folder_list:
        # 获取所有已经解压了的文件列表(从.ZMG_20230717 --> CS_PsN_Light_20230717_033003042_2575.zip)
        file_names = []
        for root, _, files in os.walk(foler):
            for afile in files:
                print(afile)
                file_path = os.path.join(foler, afile)
                (ff, ext) = os.path.splitext(afile)
                if ext != ".zip":
                    continue
                if afile.startswith('.'):  # 隱藏文件
                    continue
                file_names.append(afile)

        print(f'file_names:{file_names}')

        # 获取foler下的所有文件(包括级联的文件夹里面的),控制规模做一个格式和时间上的筛选
        for root, _, files in os.walk(foler):
            for afile in files:
                print(afile)
                full_path = os.path.join(root, afile)
                statinfo = os.stat(full_path)
                if current_timestamp - int(statinfo.st_mtime) < 86400 * 30: #只看近30天的

                    (_, ext) = os.path.splitext(afile)
                    if ext != ".zip":
                        continue
                    if not afile.startswith('.'):  # 隱藏文件
                        continue
                    print(f'full_path:{full_path}')
                    print(f'foler:{foler}')

                    # 获取压缩文件(.ZMG_20230717)里面的文件信息(CS_PsN_Light_20230717_033003042_2575.zip)
                    dir_name =  os.path.dirname(full_path)
                    print(f'dir_name:{dir_name}')
                    with ZipFile(full_path, 'r') as zip_obj:
                        file_info_list = zip_obj.infolist()
                        print(f'file_info_list[0]:{file_info_list[0]}')
                        if file_info_list[0].filename in file_names:
                            continue
                        zip_obj.extractall(dir_name)





