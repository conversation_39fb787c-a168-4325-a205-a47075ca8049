#! /bin/env python3

import sys
import argparse
import os
from shutil import copy2
from datetime import datetime

def readable_dir(prospective_dir):
  if not os.path.isdir(prospective_dir):
    raise Exception("readable_dir:{0} is not a valid path".format(prospective_dir))
  if os.access(prospective_dir, os.R_OK):
    return prospective_dir
  else:
    raise Exception("readable_dir:{0} is not a readable dir".format(prospective_dir))

def valid_date(s):
    try:
        return datetime.strptime(s, "%Y%m%d").date()
    except ValueError:
        msg = "not a valid date: {0!r}".format(s)
        raise argparse.ArgumentTypeError(msg)

def getClearingDateFromFileName(filename):
    return datetime.strptime(filename.split('_')[3][:8], "%d%m%Y").date()

def convertFileName(path, filename):
  f = open(os.path.join(path, filename), 'r')
  nameArr = filename.split('_')

  # Select the column for the clearing date
  columnName = 'Business Date' if filename.startswith('Positions_') else 'Booking Date'

  i = f.readline().split(';').index(columnName)
  dateStr = f.readline().split(';')[i]
  dateStr = datetime.strptime(dateStr, '%d.%m.%Y').date().strftime('%d%m%Y')

  # Transactions_3209747W_CSV_24052022_24052022_99356.XML.gpg
  nameArr[3] = dateStr
  nameArr[4] = dateStr

  return '_'.join(nameArr)


def main(argv):
  parser = argparse.ArgumentParser(description='Move jb files in new format to the legacy fomrat in the legacy folder.')
  parser.add_argument('-d', '--directory', type=readable_dir)
  parser.add_argument('--since', type=valid_date, default=datetime.now().date())
  parser.add_argument('--dry-run', action='store_true')

  args = vars(parser.parse_args())
  print(args)

  directory = args['directory']
  isDryRun = args['dry_run']
  since = args['since']

  # loop all .csv files in {dir}, rename if necessary
  fileCount = 0
  for f in os.listdir(directory):
    if not os.path.isfile(os.path.join(directory, f)):
      continue

    _, ext = os.path.splitext(f)
    ext = ext.lower()
    if ext not in [".csv", ".xml", ".xls"]:
      continue

    date = getClearingDateFromFileName(f)
    if (date < since):
      continue

    old = os.path.join(directory, f)
    new = os.path.join(directory, convertFileName(directory, f))

    if (old != new):
      fileCount += 1
      print(f"Renaming {old} to {new}`...")
      if (not isDryRun):
        stat = os.stat(old)
        os.rename(old, new)
        os.utime(new, (stat.st_atime, stat.st_mtime))
        print(f"Move completed.")

  print("")
  print(f"Renamed {fileCount} files in total.")

if __name__ == "__main__":
  main(sys.argv)

