#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的密码生成器
生成12位包含数字大小写字母的密码及其加密版本
用法: python generate_password.py [用户名] [密码长度]
"""

import sys
import random
import string
import crypt
import argparse


def generate_password(length=12):
    """生成指定长度的随机密码，包含数字、大小写字母"""
    characters = string.ascii_letters + string.digits
    password = ''.join(random.choice(characters) for _ in range(length))
    return password


def encrypt_password(plain_password):
    """使用系统默认方法加密密码（与crypt.crypt(pw)相同）"""
    # 使用系统默认加密方法，不指定salt参数
    encrypted = crypt.crypt(plain_password)
    return encrypted


def main():
    parser = argparse.ArgumentParser(description='生成随机密码及其加密版本')
    parser.add_argument('username', nargs='?', help='用户名（可选）')
    parser.add_argument('--length', '-l', type=int, default=12,
                       help='密码长度 (默认: 12)')
    parser.add_argument('--count', '-c', type=int, default=1,
                       help='生成密码的数量 (默认: 1)')
    
    args = parser.parse_args()
    
    username = args.username
    password_length = args.length
    count = args.count
    
    for i in range(count):
        # 生成密码
        plain_password = generate_password(password_length)
        encrypted_password = encrypt_password(plain_password)

        if username:
            # 直接输出vault.vars.yml格式
            print(f"  {username}: \"{encrypted_password}\"")
            print(f"  {username}_plain: \"{plain_password}\"")
        else:
            # 如果没有用户名，使用默认格式
            default_user = f"user{i+1}" if count > 1 else "user"
            print(f"  {default_user}: \"{encrypted_password}\"")
            print(f"  {default_user}_plain: \"{plain_password}\"")

        # 如果生成多个密码，添加空行分隔
        if count > 1 and i < count - 1:
            print()


if __name__ == "__main__":
    main()
