# 简单密码生成器

这是一个简单的密码生成工具，用于生成12位随机密码（包含数字、大小写字母）及其对应的加密版本。

## 文件说明

- `generate_password.py` - Python版本（推荐）
- `generate_password.ps1` - PowerShell版本（Windows）
- `generate_password.sh` - Bash版本（Linux/macOS）

## 功能特性

✅ 生成指定长度的随机密码（默认12位）
✅ 包含数字、大小写字母
✅ 使用系统默认方法加密密码（与 `crypt.crypt(pw)` 相同）
✅ 支持批量生成多个密码
✅ 输出vault.vars.yml格式

## 使用方法

### Python版本（推荐）

```bash
# 基本用法 - 生成一个密码
python generate_password.py

# 指定用户名
python generate_password.py testuser

# 指定密码长度
python generate_password.py --length 16

# 生成多个密码
python generate_password.py --count 3

# 完整参数
python generate_password.py testuser --length 16 --count 2
```

### PowerShell版本（Windows）

```powershell
# 基本用法
.\generate_password.ps1

# 指定用户名
.\generate_password.ps1 -Username testuser

# 指定密码长度
.\generate_password.ps1 -Length 16

# 生成多个密码
.\generate_password.ps1 -Count 3

# 完整参数
.\generate_password.ps1 -Username testuser -Length 16 -Count 2
```

### Bash版本（Linux/macOS）

```bash
# 给脚本添加执行权限
chmod +x generate_password.sh

# 基本用法
./generate_password.sh

# 指定用户名
./generate_password.sh -u testuser

# 指定密码长度
./generate_password.sh -l 16

# 生成多个密码
./generate_password.sh -c 3

# 完整参数
./generate_password.sh -u testuser -l 16 -c 2
```

## 输出示例

```
============================================================
密码生成器
============================================================
用户名: testuser
明文密码: Kj8mN2pQ9rXz
加密密码: $6$rounds=656000$YourSaltHere$YourHashHere

用于vault.vars.yml的格式:
  testuser: "$6$rounds=656000$YourSaltHere$YourHashHere"
  testuser_plain: "Kj8mN2pQ9rXz"
------------------------------------------------------------
```

## 依赖要求

### Python版本
- Python 3.6+（内置crypt模块）

### PowerShell版本
- PowerShell 5.0+
- Python 3.6+（用于SHA-512加密，可选）

### Bash版本
- Bash 4.0+
- Python 3.6+（用于SHA-512加密）或 OpenSSL（替代方案）

## 加密方式

脚本使用与以下命令相同的加密方式：
```bash
python3 -c 'import crypt,getpass;pw="VMa2bnhcRr";print(crypt.crypt(pw))'
```

## 安全特性

- 使用加密安全的随机数生成器
- 系统默认加密算法（通常是SHA-512或更强）
- 自动生成随机salt
- 符合Linux/Unix标准的密码哈希格式

## 使用场景

1. **新用户创建** - 为新SFTP用户生成安全密码
2. **密码重置** - 为现有用户生成新密码
3. **批量用户** - 一次性为多个用户生成密码
4. **测试环境** - 为测试用户生成临时密码

## 集成到vault.vars.yml

生成的密码可以直接复制到vault.vars.yml文件中：

```yaml
ftpuser_passwd:
  testuser: "$6$rounds=656000$YourSaltHere$YourHashHere"
  testuser_plain: "Kj8mN2pQ9rXz"
  user2: "$6$rounds=656000$AnotherSalt$AnotherHash"
  user2_plain: "Xy7pL4mK8nQw"
```

然后使用ansible-vault加密文件：

```bash
ansible-vault encrypt vault.vars.yml
```

## 故障排除

### Python不可用
如果Python不可用，PowerShell和Bash版本会自动降级使用SHA256哈希作为替代。

### 权限问题
确保脚本有执行权限：
```bash
chmod +x generate_password.sh
```

### 字符集问题
所有脚本都使用标准ASCII字符集（A-Z, a-z, 0-9），确保跨平台兼容性。
