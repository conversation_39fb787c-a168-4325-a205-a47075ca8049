#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time    : 2023/12/28 10:20
<AUTHOR> yuan
@File    : trans.py
@Function: 
"""
import os

# 处理单个文件的核心逻辑
def process_file(file_path):
    # 读取文件内容
    with open(file_path, 'r') as file:
        lines = file.readlines()

    # 按制表符分隔每一行数据
    semicolon_separated_lines = []
    for line in lines:
        line = line.rstrip()
        line_list = line.split('\t')
        semicolon_separated_line = ';'.join(line_list)
        semicolon_separated_lines.append(semicolon_separated_line)

    return semicolon_separated_lines

# 处理目录下所有文件的逻辑
def process_files(directory):
    # 检查目录下的文件
    for root, dirs, files in os.walk(directory):
        for file in files:
            # 检查文件是否以 .DAT 结尾并且包含 'POS'
            if file.endswith('.DAT') and 'POS' in file:
                dat_file_path = os.path.join(root, file)
                csv_file_path = os.path.splitext(dat_file_path)[0] + '.csv'

                # 如果目标文件存在，则跳过处理
                if os.path.exists(csv_file_path):
                    print(f"File '{csv_file_path}' already exists. Skipping...")
                    continue

                # 处理文件并将数据写入 .csv 文件
                semicolon_separated_lines = process_file(dat_file_path)
                with open(csv_file_path, 'w') as output:
                    for line in semicolon_separated_lines:
                        output.write(line + '\n')

                print(f"Converted '{dat_file_path}' to '{csv_file_path}'.")



# 脚本参数传入的目录路径
#target_directory = 'E:\备份Bak\Work\LANG\Python\Practice\Grammer\work\jbch\orifile'  # 替换为你要处理的目录路径
target_directory = '/data/banks/bankassets/em_wealth_julius_baer_ch/mftANCA'  # 替换为你要处理的目录路径
process_files(target_directory)



