#!/usr/bin/env python3
import os
import sys
from argparse import ArgumentParser, RawTextHelpFormatter, FileType
from io import StringIO

import pandas as pd

# 对选中的文件进行解密的具体函数实现/对“进行解密”这个按钮的监听函数
import pgpy

def decrptSelectedFile(passWordFans, secrectListPrivateFileName, file_path, new_file_path):
    """
    :param passWordFans: 解密密码
    :param secrectListPrivateFileName:  私钥文件
    :param file_path:  解压的文件
    :param new_file_path:  解压到哪个文件中
    :return:
    """
    secrectPriKey, _ = pgpy.PGPKey.from_file(secrectListPrivateFileName)
    # 对输入的口令进行判断，利用try...catch块进行
    try:
        with secrectPriKey.unlock(passWordFans):
            if secrectPriKey.is_expired:
                print("您使用的公私钥文件已经到期！")
            else:
                # 要解密的文件的内容的读写
                file_object = open(file_path, encoding="iso-8859-1")
                all_the_txt = file_object.read()
                # 从文件中读取了str类型的内容后，要对该内容进行解密需要采用以下方法，及先将已有的内容转为PGPMessage类型，注意采用的是from_blob而不是new
                beingDecrpted = pgpy.PGPMessage.from_blob(all_the_txt)
                # 利用已选中的私钥对文件进行解密
                dec_msg = secrectPriKey.decrypt(beingDecrpted)
                # 下面将解密后的文件内容生成一个新的文件，写在一个新的文件夹里
                new_file_object = open(new_file_path, 'wb')
                new_file_object.write(dec_msg.message)
                new_file_object.close()

                print(f'文件解密成功:{file_path}, 解密文件为:{new_file_path}')
    except Exception as e:
        print(e)
        print("您输入的口令不对！")

def main(args):
    work_dir = args.work_dir
    files = os.listdir(work_dir)
    os.chdir(work_dir)
    passWordFans = args.pgppwd
    secrectListPrivateFileName = args.private_file_name

    for f in files:
        if not f.endswith('.gpg'):
            continue
        basename, ext = os.path.splitext(f)
        decrypt_file_path = os.path.join(work_dir, basename)

        if os.path.isfile(decrypt_file_path): # 已经解压了
            continue

        origin_file_path = os.path.join(work_dir, f)

        # os.system('/usr/bin/gpg --output {} {} --decrypt {}'.format(decrypt_file_path, " ".join(args.gpg_args), origin_file_path))
        decrptSelectedFile(passWordFans, secrectListPrivateFileName, origin_file_path, decrypt_file_path)

        # 然后将 mtime 保持一致
        file_stat_obj = os.stat(origin_file_path)
        if os.path.isfile(decrypt_file_path): # sometimes decripty failed
            os.utime(decrypt_file_path, (file_stat_obj.st_atime, file_stat_obj.st_mtime))

if __name__ == "__main__":
    parser = ArgumentParser(description='pgp decrypt file', formatter_class=RawTextHelpFormatter)
    # --work_dir .  --pgppwd B53C5D6C --private_file_name jpm_sg_red_beacon_privateKey
    parser.add_argument('work_dir', type=str, help='work dir')
    parser.add_argument('--pgppwd', type=str, help='pgp decrpt password', default='B53C5D6C')
    parser.add_argument('--private_file_name', type=str, help='private password file position')
    args = parser.parse_args()
    main(args)
