# 简单的密码生成器 PowerShell版本
# 生成12位包含数字大小写字母的密码及其加密版本
# 用法: .\generate_password.ps1 [-Username <用户名>] [-Length <长度>] [-Count <数量>]

param(
    [Parameter(Mandatory=$false)]
    [string]$Username,
    
    [Parameter(Mandatory=$false)]
    [int]$Length = 12,
    
    [Parameter(Mandatory=$false)]
    [int]$Count = 1,
    
    [Parameter(Mandatory=$false)]
    [switch]$Help
)

# 显示帮助信息
function Show-Usage {
    Write-Host "用法: .\generate_password.ps1 [选项]"
    Write-Host "参数:"
    Write-Host "  -Username <用户名>     用户名（可选）"
    Write-Host "  -Length <长度>         密码长度 (默认: 12)"
    Write-Host "  -Count <数量>          生成密码的数量 (默认: 1)"
    Write-Host "  -Help                  显示此帮助信息"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\generate_password.ps1"
    Write-Host "  .\generate_password.ps1 -Username testuser"
    Write-Host "  .\generate_password.ps1 -Length 16 -Count 3"
    exit 0
}

# 生成随机密码
function Generate-Password {
    param([int]$Length)
    
    $chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    $password = ""
    
    for ($i = 0; $i -lt $Length; $i++) {
        $randomIndex = Get-Random -Minimum 0 -Maximum $chars.Length
        $password += $chars[$randomIndex]
    }
    
    return $password
}

# 加密密码
function Encrypt-Password {
    param([string]$PlainPassword)
    
    try {
        # 检查Python是否可用
        $null = Get-Command python -ErrorAction Stop
        
        $pythonScript = @"
import crypt
print(crypt.crypt('$PlainPassword', crypt.mksalt(crypt.METHOD_SHA512)))
"@
        $encryptedPassword = python -c $pythonScript 2>$null
        if ($LASTEXITCODE -eq 0 -and $encryptedPassword) {
            return $encryptedPassword.Trim()
        } else {
            throw "Python crypt failed"
        }
    }
    catch {
        # 如果Python不可用，使用PowerShell的哈希功能作为替代
        Write-Warning "Python不可用，使用SHA256哈希作为替代"
        $bytes = [System.Text.Encoding]::UTF8.GetBytes($PlainPassword)
        $hash = [System.Security.Cryptography.SHA256]::Create().ComputeHash($bytes)
        $hashString = [System.BitConverter]::ToString($hash) -replace '-'
        return "`$sha256`$" + $hashString.ToLower()
    }
}

# 主函数
function Main {
    # 显示帮助
    if ($Help) {
        Show-Usage
    }
    
    Write-Host "=" * 60
    Write-Host "密码生成器"
    Write-Host "=" * 60
    
    for ($i = 1; $i -le $Count; $i++) {
        if ($Count -gt 1) {
            Write-Host ""
            Write-Host "--- 密码 $i ---"
        }
        
        # 生成密码
        $plainPassword = Generate-Password -Length $Length
        $encryptedPassword = Encrypt-Password -PlainPassword $plainPassword
        
        if ($Username) {
            Write-Host "用户名: $Username"
        }
        Write-Host "明文密码: $plainPassword"
        Write-Host "加密密码: $encryptedPassword"
        
        if ($Username) {
            Write-Host ""
            Write-Host "用于vault.vars.yml的格式:"
            Write-Host "  ${Username}: `"$encryptedPassword`""
            Write-Host "  ${Username}_plain: `"$plainPassword`""
        }
        
        Write-Host "-" * 60
    }
}

# 运行主函数
Main
