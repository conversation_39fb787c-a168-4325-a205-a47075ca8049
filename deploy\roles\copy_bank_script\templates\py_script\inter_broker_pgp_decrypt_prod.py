# !/usr/bin/python3
# -*- coding: utf-8 -*-
import argparse
import os
from argparse import ArgumentParser, RawTextHelpFormatter
from datetime import datetime, timedelta, date

import gnupg


def readable_dir(prospective_dir):
    if not os.path.isdir(prospective_dir):
        raise Exception("readable_dir:{0} is not a valid path".format(prospective_dir))
    if os.access(prospective_dir, os.R_OK):
        return prospective_dir
    else:
        raise Exception("readable_dir:{0} is not a readable dir".format(prospective_dir))

def valid_date(s):
    try:
        return datetime.strptime(s, "%Y%m%d").date()
    except ValueError:
        msg = "not a valid date: {0!r}".format(s)
        raise argparse.ArgumentTypeError(msg)

def file_statinfo_2_date(file_full_path):
    """
    功能： 获取文件的时间
    file_full_path: 文件名  全路径
    :return:
    """
    statinfo = os.stat(file_full_path)
    str_date = date.fromtimestamp(statinfo.st_mtime).strftime("%Y%m%d")  # 时间转换
    # 转为时间格式
    file_stat_date = datetime.strptime(str_date, "%Y%m%d").date()
    return file_stat_date

def pgp_decrypt(origin_file_path, decrypt_file_path, pgppwd):
    """
    :param origin_file_path:  原始文件路径
    :param decrypt_file_path: 解密的目标路径
    :param pgppwd: 解密密码
    :return:
    """
    # 加载要解密的文件内容
    with open(origin_file_path, 'rb') as f:
        encrypted_data = f.read()

    # 解密文件内容
    decrypted_data = gpg.decrypt(encrypted_data, passphrase=pgppwd)

    # 将解密后的内容写入文件
    with open(decrypt_file_path, 'wb') as f:
        f.write(decrypted_data.data)

    print(f'文件解密成功: {decrypted_data.ok} 原始文件: {origin_file_path} 解密文件: {decrypt_file_path}')
    # 排错时使用
    # print(encrypted_data.stderr)   # 查看是否有相关的错误消息

def main(args):

    srcDir = args.srcDir
    destDir = args.destDir
    files = os.listdir(srcDir)
    os.chdir(srcDir)    # 切换到要解析对应的银行目录中
    pgppwd = args.pgppwd
    # 私钥路径
    secret_key_file = args.private_file_name
    since = args.since
    if not since:
        since = (datetime.now() + timedelta(days=-3)).date()
    else:
        # valid_date 函数已经转化
        # since = datetime.strptime(since, "%Y%m%d").date()
        pass


    # 加载私钥
    with open(secret_key_file, 'rb') as f:
        private_key_data = f.read()
    # 导入私钥
    gpg.import_keys(private_key_data)

    for f in files:
        if not f.endswith('.pgp'):
            continue
        basename, ext = os.path.splitext(f)
        # 解密路径
        decrypt_file_path = os.path.join(destDir, basename)
        if os.path.isfile(decrypt_file_path):    # 已经解压了
            continue

        origin_file_path = os.path.join(srcDir, f)
        # 只解压最新日期的数据 (文件修改时间 < since 的不解)
        file_stat_date = file_statinfo_2_date(origin_file_path)
        if file_stat_date < since:
            continue

        # 解密文件
        pgp_decrypt(origin_file_path, decrypt_file_path, pgppwd)

        # 然后将 mtime 保持一致
        file_stat_obj = os.stat(origin_file_path)
        if os.path.isfile(decrypt_file_path):  # sometimes decripty failed
            os.utime(decrypt_file_path, (file_stat_obj.st_atime, file_stat_obj.st_mtime))




if __name__ == "__main__":
    # 运行方式: python3 inter_broker_pgp_decrypt_prod.py --srcDir /data/banks  --destDir /data/banks/ --pgppwd B53C5D6C --private_file_name privateKey
    # --srcDir /home/<USER>/apps/dp_quotation_server/scripts/test  --destDir /home/<USER>/apps/dp_quotation_server/scripts/test/gongsiyao --pgppwd B53C5D6C --private_file_name Interactive Broker_0x99976029_SECRET.asc

    parser = ArgumentParser(description='pgp decrypt script', formatter_class=RawTextHelpFormatter)
    parser.add_argument('--srcDir', type=readable_dir, help='需要解密的银行目录')
    parser.add_argument('--destDir', type=readable_dir, help='解密到对应的目标目录')
    parser.add_argument('--since', type=valid_date, help="默认解密3天的， 有参数 解密 >= since 时间的文件数据")    # --since ********
    parser.add_argument('--pgppwd', type=str, help='pgp decrpt password', default='B53C5D6C')
    parser.add_argument('--private_file_name', type=str, help='private file name')
    args = parser.parse_args()
    # 定义gpg
    gpg = gnupg.GPG()
    main(args)