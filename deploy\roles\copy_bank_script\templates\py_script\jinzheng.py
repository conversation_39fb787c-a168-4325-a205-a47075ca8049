#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time    : 2024/3/13 10:20
<AUTHOR> yuan
@File    : jz3.py.py
@Function: 
"""
import sys

# 1. 先根据日期得到三份要处理的文件路径

# 2. 读取三份文件,得到三个(account_no,df)的map

# 3. 取一份map作为初始数据,对于后续的两个map,执行如下的逻辑

# 4. 遍历所有key
#    a. 检查原始map中是否包含这个account_no,如果没有就追加这个df到原始map
#    b. 如果有,则需比较这个df和原始map中的df是否一致,一致就跳过,不一致就退出

# 5. 自己写一个df的比较函数,先按照要比较字段排序,然后比较行数是否一致,
#    再比较每一个行关心的字段是否相等

import pandas as pd
import os
from datetime import datetime
import argparse  # 导入argparse模块

# 假设文件夹路径是固定的
folder_path = '/data/banks/uploads/jinzheng/upload'
output_folder = '/data/banks/bankassets/gsl_hk_gt'

#folder_path = 'E:\\Datafeed接入\\文件中转\\136\\upload'
#output_folder = 'E:\\Datafeed接入\\文件中转\\136\\out'

# 读取Excel文件中的两个表格
def read_excel_sheets(file_path, sheet_names):
    return {sheet_name: pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl', dtype=str) for sheet_name in sheet_names}


# 比较两个DataFrame是否相等，这里我们按照一些列进行排序和比较
def compare_dataframes(df1, df2, columns_to_compare):
    df1_sorted = df1.sort_values(by=columns_to_compare).reset_index(drop=True)
    df2_sorted = df2.sort_values(by=columns_to_compare).reset_index(drop=True)

    if df1_sorted.shape != df2_sorted.shape:
        return False

    for column in columns_to_compare:
        if not df1_sorted[column].equals(df2_sorted[column]):
            return False

    return True


# 合并和比较所有DataFrame的函数
def merge_and_compare(accounts_data, columns_to_compare_by_sheet):
    final_data = {}
    for file_path, sheets_data in accounts_data.items():
        for sheet_name, df in sheets_data.items():
            columns_to_compare = columns_to_compare_by_sheet.get(sheet_name, [])
            if sheet_name not in final_data:
                final_data[sheet_name] = df
            else:
                for account_no in df['Client number'].unique():
                    # 检查是否已存在账户
                    if account_no not in final_data[sheet_name]['Client number'].unique():
                        final_data[sheet_name] = pd.concat([final_data[sheet_name], df[df['Client number'] == account_no]],
                                                           ignore_index=True)
                    else:
                        # 比较两个DataFrame
                        existing_df = final_data[sheet_name][final_data[sheet_name]['Client number'] == account_no]
                        new_df = df[df['Client number'] == account_no]
                        if not compare_dataframes(existing_df, new_df, columns_to_compare):
                            # TODO: 发送微信机器人告警
                            print(f'账户数据不一致: {account_no}')
                            sys.exit()

    return final_data


# 最终的保存合并数据的函数
def save_final_data(final_data, output_folder, date_str):
    for sheet_name, df in final_data.items():
        file_name=''
        if '持仓' in sheet_name:
            file_name = 'holdings'
        elif '资金' in sheet_name:
            file_name = 'cash'
        output_file = os.path.join(output_folder, f"{file_name}_{date_str}.csv")
        # 检查文件是否存在
        # 检查文件是否存在
        if os.path.exists(output_file):
            if args.f:
                # 替换现有文件
                print(f"{output_file} 文件已存在，将被替换")
                df.to_csv(output_file, index=False, sep='\t')
            else:
                print(f"{output_file} 文件已存在, 需要替换请使用 -f 选项")
        else:
            # 将 DataFrame 写入到文件中
            df.to_csv(output_file, index=False, sep='\t',encoding='utf-8')


# 主函数
def main(date_str):
    # 构建文件路径
    file_paths = []
    for subdir, dirs, files in os.walk(folder_path):
        for file_name in files:
            if date_str in file_name:
                file_paths.append(os.path.join(subdir, file_name))

    # 读取文件
    sheet_names = [f'持仓{date_str}', f'资金{date_str}']
    accounts_data = {}
    for file_path in file_paths:
        if os.path.exists(file_path):
            accounts_data[file_path] = read_excel_sheets(file_path, sheet_names)
        else:
            print(f'文件不存在: {file_path}')  # 这里可以替换为微信机器人告警


    # 读取文件并合并数据
    columns_to_compare_by_sheet = {
        f'持仓{date_str}': ['Assetclass', 'code', 'Quantity', 'Market value(ccy)'],
        f'资金{date_str}': ['Total assets(HKD)', 'Total assets(USD)', 'Total assets(CNY)']
    }
    final_data = merge_and_compare(accounts_data, columns_to_compare_by_sheet)


    # 保存最终数据到CSV
    #output_folder = 'E:\\Datafeed接入\\文件中转\\136\\out'  # 输出文件夹路径
    save_final_data(final_data, output_folder,date_str)


# 测试主函数


if __name__ == '__main__':
    # 创建ArgumentParser对象
    parser = argparse.ArgumentParser(description='处理账户数据')
    # 添加日期参数，格式为YYYYMMDD，默认为当前日期
    parser.add_argument('-d', '--date', type=str, default=datetime.today().strftime('%Y%m%d'),
                        help='要处理的数据日期，格式为YYYYMMDD，默认为今天的日期')
    # 是否替换，默认false
    parser.add_argument('-f', action='store_true', help='force replace existing file')
    # 解析命令行参数
    args = parser.parse_args()

    # 调用main函数，传递日期参数
    main(args.date)
