#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试输出格式
"""

import sys
import os

# 添加当前目录到路径，以便导入generate_password模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from generate_password import generate_password, encrypt_password

def test_output():
    print("测试输出格式:")
    print()
    
    # 测试单个用户
    username = "dts053"
    plain_password = generate_password(10)  # 生成10位密码作为示例
    encrypted_password = encrypt_password(plain_password)
    
    print(f"  {username}: \"{encrypted_password}\"")
    print(f"  {username}_plain: \"{plain_password}\"")
    
    print()
    print("=" * 50)
    print("这个输出可以直接复制到vault.vars.yml文件中")

if __name__ == "__main__":
    test_output()
