#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time    : 2024/3/11 9:18
<AUTHOR> yuan
@File    : hsbc.py.py
@Function:
"""
# 1. 检测csv文件是否是已知类型,出现新的就告警
# 2. 将各个机构的文件同步到正确的文件夹
# 3. 处理增量同步的问题


import os
import re
import subprocess
import time
from datetime import datetime
import requests

# 监控目录路径
watched_dir = '/data/banks/bankassets/hsbc_hk_sg/Inbox'

# 机构对应的目标目录路径
target_dirs = {
    'Gentleman': '/data/banks/bankassets/hsbc_gentleman',
    'Poseidon': '/data/banks/bankassets/hsbc_poseidon',
    'Avenue': '/data/banks/bankassets/hsbc_avenue',
    'Peakhouse': '/data/banks/bankassets/hsbc_peakhouse'
}

# 企业微信机器人的API接口信息
wechat_robot_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1a09da1a-6d97-44ac-bf94-135b272c094a'

# 文件名模式
file_pattern = re.compile(r'ESVW000([1-4])00\d{8}_(POS|TRN)\.csv$')

def send_wechat_alert(message):
    """发送企业微信告警消息"""
    payload = {
        "msgtype": "text",
        "text": {
            "content": message
        }
    }
    response = requests.post(wechat_robot_url, json=payload)
    if response.status_code == 200:
        print("企业微信告警发送成功")
    else:
        print("企业微信告警发送失败，状态码：", response.status_code)

def sync_file(file_name, agency):
    """使用rsync同步文件到对应机构的目录"""
    source_path = os.path.join(watched_dir, file_name)
    dest_path = target_dirs[agency]
    cmd = ['rsync', '-av', source_path, dest_path]
    subprocess.run(cmd, check=True)
    print(f"文件 {file_name} 已同步到 {agency} 的目录")

def process_files():
    """处理目录中的所有文件"""
    for file_name in os.listdir(watched_dir):
        if not file_name.endswith('.csv'):
            continue

        match = file_pattern.match(file_name)
        if match:
            agency_code = match.group(1)
            agency_mapping = {'1': 'Gentleman', '2': 'Poseidon', '3': 'Avenue', '4': 'Peakhouse'}
            agency = agency_mapping.get(agency_code)
            if agency:
                sync_file(file_name, agency)
            else:
                send_wechat_alert(f"无法识别的机构代码在文件中: {file_name}")
        else:
            send_wechat_alert(f"文件名不符合要求: {file_name}")



process_files()

print('finished')

