default:
  before_script:
  - command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )
  - ssh-agent -s > SSH-AGENT
  - eval $(cat SSH-AGENT)
  - echo "${DEPLOY_BOT_SSH_PRIVATE_KEY}" | base64 -d -w0 | tr -d '\r' | ssh-add -
  - echo "${EVCI_RSA_DEPLOY_SSH_PRIVATE_KEY}" | base64 -d -w0 | tr -d '\r' | ssh-add -
  - echo "${ANSIBLE_VAULT_PASSWORD_DEVOPS}" | base64 -d  > "${ANSIBLE_VAULT_PASSWORD_FILE}"
  - echo $(sha256sum "${ANSIBLE_VAULT_PASSWORD_FILE}")
  - export ANSIBLE_INVENTORY=hosts.ini
  - export ANSIBLE_HOST_KEY_CHECKING=false
  - export ANSIBLE_GATHERING=smart
  - export ANSIBLE_BECOME_PASS=$(echo $UBUNTU_ANSIBLE_BECOME_PASS_BASE64 | base64 -d -w0 | tr -d '\r')

copy_bank_script:
  only:
  - main
  stage: copy_script_to_prod_host
  tags:
  - ev140_java
  when: manual
  script:
  - cd deploy
  - |
    ansible-playbook \
    copy_bank_script_playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_BANK_HOSTS=SFTP-DATA \
    -u ${ANSIBLE_USER:-root} \
    -b
