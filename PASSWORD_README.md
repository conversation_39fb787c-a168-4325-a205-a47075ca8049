# 密码生成器

简单的bash脚本，用于生成12位随机密码（包含数字、大小写字母）及其加密版本。

## 使用方法

```bash
# 给脚本添加执行权限
chmod +x generate_password.sh

# 基本用法
./generate_password.sh <用户名>

# 指定密码长度
./generate_password.sh <用户名> <密码长度>
```

## 示例

```bash
# 为用户dts053生成12位密码
./generate_password.sh dts053

# 为用户dts053生成16位密码
./generate_password.sh dts053 16
```

## 输出格式

```
  dts053: "$6$BSfbQmagS.YFcN0z$M9T86wHgx9hJ33FtwDFnNtZ1VEPV5n0MxS.wpNN24.8hhuNp8dMYXy238ZG3wf.n.D8iwHroesrzM6y6rzEoy0"
  dts053_plain: "jFzC7CtCvL"
```

输出可以直接复制到vault.vars.yml文件的ftpuser_passwd部分。

## 加密方式

使用与以下命令相同的加密方式：
```bash
python3 -c 'import crypt,getpass;pw="VMa2bnhcRr";print(crypt.crypt(pw))'
```

## 依赖要求

- Bash 4.0+
- Python 3.6+（用于密码加密）

## 使用流程

1. 运行脚本生成密码
2. 复制输出到vault.vars.yml
3. 使用ansible-vault加密文件
4. 部署用户
