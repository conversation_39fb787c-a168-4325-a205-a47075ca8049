#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time    : 2023/12/28 15:01
<AUTHOR> yuan
@File    : process.py
@Function: 
"""
import os
import sys
import pandas as pd
from datetime import datetime, timedelta


# 检查当前时间是否超过当天18点
# 获取当前时间
now = datetime.now()

# 获取今天的18点
today_6pm = now.replace(hour=19, minute=0, second=0, microsecond=0)

if now > today_6pm:
    print(f'当前时间 {now} 已超过当天18点，脚本将退出。')
    sys.exit()

print(f'当前处理时间: {now}')


# 指定输入和输出目录路径
input_directory = '/data/banks/uploads/uob_kh_hk_rockpool/uob_kh_hk_rockpool'  # 替换为你的目录路径
output_directory = '/data/banks/bankassets/uob_kh_hk_rockpool'  # 输出目录路径

# 获取输入目录下所有文件
files = os.listdir(input_directory)

# 计算7天前的日期
seven_days_ago = datetime.now() - timedelta(days=7)

# 创建一个字典，用于按文件类型和日期存储文件路径
file_dict = {}
for file in files:
    if file.endswith('.csv'):
        parts = file.split('_')
        account_type = parts[1]
        date_str = parts[-1].split('.')[0]
        date = datetime.strptime(date_str, '%Y%m%d').date()

        # 如果文件日期超过7天，则跳过处理
        if date < seven_days_ago.date():
            #print(f"Skipping file '{file}' as it's older than 7 days.")
            continue

        key = f"{account_type}_{date_str}"
        if key not in file_dict:
            file_dict[key] = []
        file_dict[key].append(os.path.join(input_directory, file))

# 合并文件并保存到输出目录
for key, file_list in file_dict.items():

    dfs = []
    for file in file_list:
        df = pd.read_csv(file)  # 读取单个文件
        dfs.append(df)
    merged_df = pd.concat(dfs, ignore_index=True)  # 合并为单个 DataFrame

    # 提取文件类型和日期
    account_type, date = key.split('_')

    # 生成新文件名
    new_filename = f"RockPool_{account_type}_{date}.csv"
    output_path = os.path.join(output_directory, new_filename)

    # 如果目标文件存在，则跳过处理
    if os.path.exists(output_path):
        print(f"File '{output_path}' already exists. Skipping...")
        continue

    # 将合并后的 DataFrame 写入新文件（保存到输出目录）
    merged_df.to_csv(output_path, index=False)
    print(f"Files merged and saved as '{new_filename}' in '{output_directory}'")



