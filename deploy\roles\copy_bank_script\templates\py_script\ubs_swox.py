#! /bin/env python3

import sys
import argparse
import os
from shutil import copy2
from datetime import datetime
import zipfile
import re
import time

#def init(_args):



def readable_dir(prospective_dir):
  if not os.path.isdir(prospective_dir):
    raise Exception("readable_dir:{0} is not a valid path".format(prospective_dir))
  if os.access(prospective_dir, os.R_OK):
    return prospective_dir
  else:
    raise Exception("readable_dir:{0} is not a readable dir".format(prospective_dir))


#def get_folder_date(file_name):
#    search = re.search(r'.([A-Z])+_(\d{8,8}).zip$', file_name)
#    datetime_str = search.group(2)
#    return datetime_str

def get_folder_date(file_name):
    parts = file_name.split('_')
    datetime_str = parts[1][:8]  # Get the first 8 characters of the second part
    return datetime_str

def process_auto(srcDir, destDir):
    # 遍历srcDir下的文件夹
    for root, _, files in os.walk(srcDir):
        files.sort(reverse=True) # 按日期倒序
        for afile in files:
            dir_break_flag = False
            if len(afile) == 7:
                continue
            full_path = os.path.join(root, afile)
            print(full_path)
            try:
                zfile = zipfile.ZipFile(full_path, 'r')
            #print(zfile.infolist())
            except Exception as e:
                print(f"unzip faild: {full_path}")
                continue
            # 获取time信息
            zip_stat_obj = os.stat(full_path)

            # 通过正则获取zip文件中的日期
            folder_name = get_folder_date(afile)
            # 拼接目标路径,判断是否存在该文件夹,不存在则创建
            full_dest = os.path.join(destDir,folder_name)
            if not os.path.exists(full_dest):
                os.makedirs(full_dest)


            for file in zfile.infolist():
                # 获取zip中的文件名,判断目标路径中是否存在改文件,如果是则退出这一文件夹的处理(zaq)
                file_name = file.filename
                file_full_path =  os.path.join(full_dest,file_name)

                if not os.path.exists(file_full_path):
                    # 如果不存在将 zip中的文件解压到目标文件夹
                    #print(file_full_path)
                    zfile.extract(file_name, path=full_dest, pwd=None)
                    # 修改m_time和zip文件一致
                    #os.utime(file_full_path,(zip_stat_obj.st_atime,zip_stat_obj.st_mtime))a
                    print(f'{time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()))},{file.filename}')
                else:
                    # 如果存在,直接退出当前文件夹
                    dir_break_flag = True
            if dir_break_flag:
                break


def process_files(srcDir, files, destDir):
    for file in files:
        full_path = os.path.join(srcDir, file)
        folder_name = get_folder_date(file)
        full_dest = os.path.join(destDir, folder_name)
        if not os.path.exists(full_dest):
            os.makedirs(full_dest)

        zip_stat_obj = os.stat(full_path)
        zfile = zipfile.ZipFile(full_path, 'r')
        for f2 in zfile.infolist():
            file_name = f2.filename
            file_full_path = os.path.join(full_dest, file_name)
            if not os.path.exists(file_full_path):
                zfile.extract(file_name, path=full_dest, pwd=None)
                #os.utime(file_full_path, (zip_stat_obj.st_atime, zip_stat_obj.st_mtime))


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Process ubs files and load to the legacy folder.')
    parser.add_argument('-s', '--srcDir', type=readable_dir)
    parser.add_argument('-d', '--destDir', type=readable_dir)
    parser.add_argument('-f', '--files',nargs='*', type=str,help='指定要处理的.zip文件,需要-s指定到具体的文件夹 如ZAQ')

    args = parser.parse_args()
    srcDir = args.srcDir
    destDir = args.destDir
    files = args.files

    if files:
        process_files(srcDir, files, destDir)
    else:
        process_auto(srcDir, destDir)



