#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time    : 2024/6/4 10:13
<AUTHOR> yuan
@File    : gtja.py
@Function: 
"""
import json
import os
import pandas as pd
import platform
from datetime import datetime, timedelta

import requests

webhook_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=41a0b7ea-84cf-4e1d-99f8-aba47ca64dc9'

def send_msg(content):
    message = {
        "msgtype": "text",
        "text": {
            "content": "gtja 结算日提取异常 ：\n" + content
        }
    }

    response = requests.post(webhook_url, data=json.dumps(message))

def build_file_path(*path_parts):
    # 检测操作系统类型
    os_type = platform.system()

    # 根据操作系统类型设置基础路径
    if os_type == 'Windows':
        base_path = 'E:\\Code\\file_pre_process'
    else:
        # Linux或其他类Unix环境下的文件路径
        base_path = '/'

    # 将基础路径与传入的子路径拼接起来
    full_path = os.path.join(base_path, *path_parts)

    return full_path


def convert_sheets_to_csv(input_file, output_dir, force=False):
    #xls = pd.ExcelFile(input_file)
    #xls = pd.read_excel(input_file, engine='openpyxl')
    xls = pd.ExcelFile(input_file, engine='openpyxl')
    # 提取并检查所有sheet中的Report Date
    report_dates = []
    for sheet_name in xls.sheet_names:
        df = pd.read_excel(xls, sheet_name=sheet_name)
        if 'Report Date' not in df.columns:
            raise ValueError(f"'Report Date' column is missing in sheet {sheet_name}")
        report_dates.extend(df['Report Date'].dropna().unique())

    if len(set(report_dates)) != 1:
        send_msg(input_file)
        raise ValueError("Inconsistent 'Report Date' values found across sheets")

    date_str = report_dates[0]
    #date_str = pd.to_datetime(report_date).strftime('%Y%m%d')

    for sheet_name in xls.sheet_names:
        df = pd.read_excel(xls, sheet_name=sheet_name)
        output_file = os.path.join(output_dir, f"{sheet_name.replace(' ','_')}_{date_str}.csv")

        if os.path.exists(output_file) and not force:
            print(f"File {output_file} already exists. Use force=True to overwrite.")
            continue

        df.to_csv(output_file, index=False)
        print(f"Saved {output_file}")


def process_directory(input_dir, output_dir, force=False):
    now = datetime.now()
    ten_days_ago = now - timedelta(days=10)

    for file_name in os.listdir(input_dir):
        input_file = os.path.join(input_dir, file_name)

        if not file_name.endswith('.xlsx'):
            continue

        file_mtime = datetime.fromtimestamp(os.path.getmtime(input_file))
        if file_mtime < ten_days_ago:
            continue

        try:
            convert_sheets_to_csv(input_file, output_dir, force)
        except Exception as e:
            print(f"Error processing {input_file}: {e}")

#pdf_path = '202403-Merit.pdf'
input_dir = build_file_path('data','banks','uploads','GTJA_HK_Avenue','upload')
#pdf_path = build_file_path('data','banks','uploads','springboard_db_sg','db_sg','db_statement_24apr24.pdf')
output_dir = build_file_path('data','banks','bankassets','gtja_hk_avenue')

# 确保输出目录存在
os.makedirs(output_dir, exist_ok=True)

# 转换并保存csv文件
process_directory(input_dir, output_dir, force=False)

