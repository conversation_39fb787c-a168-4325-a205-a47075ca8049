default:
  before_script:
  - command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )
  - ssh-agent -s > SSH-AGENT
  - eval $(cat SSH-AGENT)
  - echo "${DEPLOY_BOT_SSH_PRIVATE_KEY}" | base64 -d -w0 | tr -d '\r' | ssh-add -
  - echo "${EVCI_RSA_DEPLOY_SSH_PRIVATE_KEY}" | base64 -d -w0 | tr -d '\r' | ssh-add -
  - echo "${UBUNTU_RSA_DEPLOY_SSH_PRIVATE_KEY_BASE64}" | base64 -d -w0 | tr -d '\r' | ssh-add -
  - echo "${ANSIBLE_VAULT_PASSWORD_DEVOPS}" | base64 -d  > "${ANSIBLE_VAULT_PASSWORD_FILE}"
  - echo $(sha256sum "${ANSIBLE_VAULT_PASSWORD_FILE}")
  - export ANSIBLE_INVENTORY=hosts.ini
  - export ANSIBLE_HOST_KEY_CHECKING=false
  - export ANSIBLE_GATHERING=smart
  - export ANSIBLE_BECOME_PASS=$(echo $UBUNTU_ANSIBLE_BECOME_PASS_BASE64 | base64 -d -w0 | tr -d '\r')

Test_Add_new_sftp_users:
  only:
  - main
  stage: sftp_users_test
  tags:
  - ev140_java
  variables:
    DEPLOY_SFTP_HOSTS: SFTP-DATA-TEST
    ftp_user: dts077
    ftp_passwd_plain: dts077_plain
    ftp_bankname: dbs_sg_owa
    ftp_bankname_sub: dbs_sg_owa
  when: manual
  script:
  - cd deploy
  - |
    ansible-playbook \
    sftp_users_test_playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_SFTP_HOSTS=${DEPLOY_SFTP_HOSTS:-SFTP-DATA-TEST} \
    -e ftp_user=${ftp_user} \
    -e ftp_bankname=${ftp_bankname} \
    -e ftp_bankname_sub=${ftp_bankname_sub} \
    -e to_modify=${to_modify:-false} \
    -u ${ANSIBLE_USER:-root} \
    -b \
    ${ARGS} 

Prod_Add_new_sftp_users:
  only:
  - main
  stage: sftp_users
  tags:
  - ev140_java
  variables:
    DEPLOY_SFTP_HOSTS: SFTP-DATA
    ftp_user: dts077
    ftp_passwd_plain: dts077_plain
    ftp_bankname: dbs_sg_owa
    ftp_bankname_sub: dbs_sg_owa
  when: manual
  script:
  - cd deploy
  - |
    ansible-playbook \
    sftp_users_playbook.yml \
    -e @../vault.vars.yml \
    -e DEPLOY_SFTP_HOSTS=${DEPLOY_SFTP_HOSTS:-SFTP-DATA} \
    -e ftp_user=${ftp_user} \
    -e ftp_bankname=${ftp_bankname} \
    -e ftp_bankname_sub=${ftp_bankname_sub} \
    -e to_modify=${to_modify:-false} \
    -u ${ANSIBLE_USER:-root} \
    -b

