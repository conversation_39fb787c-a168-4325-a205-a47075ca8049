# !/usr/bin/python3
# -*- coding: utf-8 -*-
import logging
import sys
from logging.handlers import RotatingFileHandler
import paramiko
import os
from stat import S_ISDIR as isdir
import time
import zipfile

class Myftp:
    def __init__(self,username = "",password="",host="",port=22, pkey=""):
        self.username = username
        self.password = password
        self.host = host
        self.port = port
        self.pkey = pkey

    # 公钥sftp连接模式
    def sftp_rsakey_connect(self):
        try:
            key = paramiko.RSAKey.from_private_key_file(self.pkey, password=self.password)
            t = paramiko.Transport((self.host, int(self.port)))
            t.connect(username=self.username, pkey=key)
            sftp = paramiko.SFTPClient.from_transport(t)
        except Exception as e:
            logging.info(e)
            print(e)

        return t, sftp

    #断开连接
    def sftp_disconnect(self,client, sftp):
        try:
            sftp.close()
            client.close()
        except Exception as error:
            logging.info(error)

    #检查目录
    def _check_local(self,local):
        if not os.path.exists(local):
            try:
                os.mkdir(local)
            except IOError as err:
                logging.info(err)

    #下载文件
    def sftp_downloadfile(self, sftp, remote, local):

        #检查远程文件是否存在
        try:
            result = sftp.stat(remote)
        except IOError as err:
            error = '[ERROR %s] %s: %s' %(err.errno,os.path.basename(os.path.normpath(remote)),err.strerror)
            logging.info(error)
        else:
            #判断远程文件是否为目录
            if isdir(result.st_mode):
                dirname = os.path.basename(os.path.normpath(remote))
                local = os.path.join(local, dirname)
                # 此处目的为了使下载的文件，直接解析到本地目录
                if local == root_path:
                    local = os.path.split(local)[0]
                self._check_local(local)

                sftp_listdir = sftp.listdir(remote)
                # 循环扫描文件
                logging.info(f"远程路径: {remote}, 远程路径的文件列表: {sftp_listdir}")
                for file in sftp_listdir:
                    sub_remote = os.path.join(remote, file)
                    sub_remote = sub_remote.replace('\\', '/')
                    self.sftp_downloadfile(sftp, sub_remote, local)
            else:
                t1 = time.time()
                #拷贝文件
                r_mtime = sftp.stat(remote).st_mtime   # 远程修改的时间
                t3 = time.time()
                print(f"获取文件{remote}的时间{t3 - t1}")
                # r_atime = sftp.stat(remote).st_atime   # 远程访问时间
                r_file_modify_time = time.strftime('%Y-%m-%d', time.localtime(r_mtime))    # time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(r_mtime))
                # print('远程文件修改时间：',r_file_modify_time)
                if os.path.isdir(local):
                    local = os.path.join(local,os.path.basename(remote))

                # 只下载修改时间是当天的文件
                if r_file_modify_time >= time.strftime('%Y-%m-%d', time.localtime()):
                    #判断本地是否有该文件，没有则下载，有则判断文件最后修改时间是否一致，不一致则重新下载
                    if os.path.exists(local):
                        l_mtime = os.stat(local).st_mtime   # 最后一次修改的时间
                        # l_file_modify_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(l_mtime))
                        #print('本地文件修改时间：',l_file_modify_time)
                        if r_mtime != l_mtime:
                            sftp.get(remote,local)
                            os.utime(local, (r_mtime, r_mtime))
                            logging.info(f'[get],{local},<==,{remote}')
                    else:
                        sftp.get(remote, local)
                        os.utime(local, (r_mtime, r_mtime))
                        logging.info(f'[get],{local},<==,{remote}')
                else:
                    pass
                t2 = time.time()
                print(f"执行{remote}的时间{t2 - t1}")

    # 日志信息
    def init_logging(self, prefix='default', logger_name=None):
        '''
        初始化日志
        '''
        cwd = os.getcwd()
        logdir = '/data/logs/'

        # 如果本地目录结构不存在，递归创建
        if not os.path.exists(os.path.dirname(logdir)):
            os.makedirs(os.path.dirname(logdir))

        level = logging.INFO
        stdout = 1
        max_bytes = 4096 * 1024  # 4K 分片
        backup_count = 20  # 最多20个文件
        file_log = 1

        logger_format = logging.Formatter(
            '[{}] - [%(asctime)s][%(levelname)s][%(pathname)s:%(lineno)d]: %(message)s'.format(prefix))

        root_logger = logging.getLogger(logger_name)  # 创建一个logger
        root_logger.setLevel(level)  # 这里表示 logger 能够通过的日志等级
        setattr(root_logger, 'prefix', prefix)
        logger_handlers = []

        if file_log:
            file_handler = RotatingFileHandler("{}/{}.log".format(logdir, prefix),
                                               "a",
                                               max_bytes,
                                               backup_count,
                                               encoding='utf-8')
            file_handler.setFormatter(logger_format)
            file_handler.setLevel(level)  # 表示 handle 能够通过的日志等级
            root_logger.addHandler(file_handler)

        if stdout:
            stdout_handler = logging.StreamHandler(sys.stdout)
            stdout_handler.setFormatter(logger_format)
            stdout_handler.setLevel(level)
            root_logger.addHandler(stdout_handler)

        logging.lastResort = None



def unzipAndRemoveZmg(file_path):
    # 做一个过滤
    if os.path.basename(file_path) != 'ZMG.zip':
        return
    # 解压文件
    with zipfile.ZipFile(file_path, 'r') as zip_ref:
        zip_ref.extractall(os.path.dirname(file_path))

    # 移除 .zmg 文件
    os.remove(file_path)
    print(f"File '{file_path}' removed successfully")


if __name__ == '__main__':
    # 设置本地文件存储路径
    local = "/data/banks/bankassets/rockpool_cs_hk"
    # 设置远程host路径
    remote = './download/'
    # 远程连接信息
    username = "********"
    password = ""
    host = "ebics2.credit-suisse.com"
    port = "53022"
    pkey = "/opt/keys/lgt/public/ssh_rsa"


    # 初始化
    my_ftp = Myftp(username=username, password=password, host=host, port=port, pkey=pkey)
    my_ftp.init_logging("sftp_ubs_sg_risen")
    logging.info("========执行脚本Start========")

    # 全局变量
    root_path = os.path.join(local, os.path.basename(os.path.normpath(remote)))  # 用于过滤这一次目录创建

    # 私钥连接方式
    client, my_sftp = my_ftp.sftp_rsakey_connect()
    # 下载文件
    my_ftp.sftp_downloadfile(my_sftp, remote, local)
    # 关闭资源
    my_ftp.sftp_disconnect(client, my_sftp)
	# 解压并删除ZMG.zip文件
    zmgFile = f'{local}/ZMG/ZMG.zip'
    unzipAndRemoveZmg(zmgFile)
	
    logging.info("========执行脚本End========")


