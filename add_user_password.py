#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动生成用户密码并更新vault.vars.yml文件的脚本
用法: python add_user_password.py <用户名> [vault密码文件路径]
"""

import sys
import os
import random
import string
import subprocess
import tempfile
import yaml
from pathlib import Path
import crypt
import argparse


def generate_password(length=12):
    """生成指定长度的随机密码，包含数字、大小写字母"""
    characters = string.ascii_letters + string.digits
    password = ''.join(random.choice(characters) for _ in range(length))
    return password


def encrypt_password(plain_password):
    """使用SHA-512加密密码"""
    # 生成随机salt
    salt = crypt.mksalt(crypt.METHOD_SHA512)
    # 加密密码
    encrypted = crypt.crypt(plain_password, salt)
    return encrypted


def decrypt_vault_file(vault_file_path, vault_password_file=None):
    """解密vault文件并返回内容"""
    cmd = ['ansible-vault', 'decrypt', '--output=-', vault_file_path]
    
    if vault_password_file:
        cmd.extend(['--vault-password-file', vault_password_file])
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"错误：无法解密vault文件: {e}")
        print(f"错误输出: {e.stderr}")
        return None


def encrypt_vault_file(content, vault_file_path, vault_password_file=None):
    """加密内容并写入vault文件"""
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yml') as temp_file:
        temp_file.write(content)
        temp_file_path = temp_file.name
    
    try:
        cmd = ['ansible-vault', 'encrypt', temp_file_path]
        
        if vault_password_file:
            cmd.extend(['--vault-password-file', vault_password_file])
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        # 读取加密后的内容
        with open(temp_file_path, 'r') as f:
            encrypted_content = f.read()
        
        # 写入目标文件
        with open(vault_file_path, 'w') as f:
            f.write(encrypted_content)
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误：无法加密vault文件: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)


def update_vault_passwords(username, plain_password, encrypted_password, vault_content):
    """更新vault内容中的密码"""
    try:
        # 解析YAML内容
        data = yaml.safe_load(vault_content) or {}
        
        # 确保ftpuser_passwd字典存在
        if 'ftpuser_passwd' not in data:
            data['ftpuser_passwd'] = {}
        
        # 添加加密密码和明文密码
        data['ftpuser_passwd'][username] = encrypted_password
        data['ftpuser_passwd'][f"{username}_plain"] = plain_password
        
        # 转换回YAML格式
        updated_content = yaml.dump(data, default_flow_style=False, allow_unicode=True)
        return updated_content
    except yaml.YAMLError as e:
        print(f"错误：解析YAML内容失败: {e}")
        return None


def main():
    parser = argparse.ArgumentParser(description='为用户生成密码并更新vault.vars.yml文件')
    parser.add_argument('username', help='用户名')
    parser.add_argument('--vault-file', '-f', default='vault.vars.yml', 
                       help='vault文件路径 (默认: vault.vars.yml)')
    parser.add_argument('--vault-password-file', '-p', 
                       help='vault密码文件路径')
    parser.add_argument('--password-length', '-l', type=int, default=12,
                       help='密码长度 (默认: 12)')
    
    args = parser.parse_args()
    
    username = args.username
    vault_file_path = args.vault_file
    vault_password_file = args.vault_password_file
    password_length = args.password_length
    
    # 检查vault文件是否存在
    if not os.path.exists(vault_file_path):
        print(f"错误：vault文件 {vault_file_path} 不存在")
        sys.exit(1)
    
    # 检查ansible-vault命令是否可用
    try:
        subprocess.run(['ansible-vault', '--version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误：ansible-vault命令不可用，请确保已安装Ansible")
        sys.exit(1)
    
    print(f"为用户 '{username}' 生成密码...")
    
    # 生成密码
    plain_password = generate_password(password_length)
    encrypted_password = encrypt_password(plain_password)
    
    print(f"生成的明文密码: {plain_password}")
    print(f"生成的加密密码: {encrypted_password}")
    
    # 解密vault文件
    print("正在解密vault文件...")
    vault_content = decrypt_vault_file(vault_file_path, vault_password_file)
    if vault_content is None:
        sys.exit(1)
    
    # 更新密码
    print("正在更新密码...")
    updated_content = update_vault_passwords(username, plain_password, encrypted_password, vault_content)
    if updated_content is None:
        sys.exit(1)
    
    # 备份原文件
    backup_path = f"{vault_file_path}.backup"
    print(f"备份原文件到: {backup_path}")
    with open(vault_file_path, 'r') as src, open(backup_path, 'w') as dst:
        dst.write(src.read())
    
    # 加密并保存更新后的内容
    print("正在加密并保存更新后的vault文件...")
    if encrypt_vault_file(updated_content, vault_file_path, vault_password_file):
        print("✅ 成功更新vault文件!")
        print(f"用户: {username}")
        print(f"密码: {plain_password}")
        print(f"备份文件: {backup_path}")
    else:
        print("❌ 更新vault文件失败")
        # 恢复备份
        with open(backup_path, 'r') as src, open(vault_file_path, 'w') as dst:
            dst.write(src.read())
        print("已恢复原文件")
        sys.exit(1)


if __name__ == "__main__":
    main()
