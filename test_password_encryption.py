#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试密码加密方式是否与指定命令一致
"""

import crypt

def test_encryption():
    # 测试密码
    test_password = "VMa2bnhcRr"
    
    print("测试密码加密方式")
    print("=" * 50)
    print(f"测试密码: {test_password}")
    print()
    
    # 方式1：使用crypt.crypt(pw) - 您要求的方式
    encrypted1 = crypt.crypt(test_password)
    print(f"方式1 - crypt.crypt(pw): {encrypted1}")
    
    # 方式2：使用系统默认salt
    encrypted2 = crypt.crypt(test_password, None)
    print(f"方式2 - crypt.crypt(pw, None): {encrypted2}")
    
    # 方式3：使用SHA-512
    salt_sha512 = crypt.mksalt(crypt.METHOD_SHA512)
    encrypted3 = crypt.crypt(test_password, salt_sha512)
    print(f"方式3 - SHA512: {encrypted3}")
    
    print()
    print("注意：每次运行结果可能不同，因为salt是随机生成的")
    print("但加密格式应该一致")

if __name__ == "__main__":
    test_encryption()
