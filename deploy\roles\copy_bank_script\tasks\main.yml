---
- name: test
  shell: ls -lrt
  register: list_info
- name: shell module return values
  debug:
    var: list_info.stdout_lines
- name: 创建 mikdir 目录
  file:
    path: "{{PROJECT_HOME}}/{{PROJECT_DIR}}"
    state: directory
    mode: '0755'
    owner: root
    group: root
#    - name: 拷贝存量 python script to host
#      template:
#        src: "{{item}}"
#        dest: "{{PROJECT_HOME}}/{{PROJECT_DIR}}"
#        mode: '0644'
#        owner: root
#        group: root
#      with_fileglob:
#        - "py_script/*.py"
#    - name: 拷贝存量 shell script to host
#      template:
#        src: "{{item}}"
#        dest: "{{PROJECT_HOME}}/{{PROJECT_DIR}}"
#        mode: '0755'
#        owner: root
#        group: root
#      with_fileglob:
#        - "sh_script/*.sh"
- name: 拷贝新增 python script to host
  template:
    src: "{{item}}"
    dest: "{{PROJECT_HOME}}/{{PROJECT_DIR}}"
    mode: '0644'
    owner: root
    group: root
  with_fileglob:
    - "py_script_new/*.py"
- name: 拷贝新增 shell script to host
  template:
    src: "{{item}}"
    dest: "{{PROJECT_HOME}}/{{PROJECT_DIR}}"
    mode: '0755'
    owner: root
    group: root
  with_fileglob:
    - "sh_script_new/*.sh"