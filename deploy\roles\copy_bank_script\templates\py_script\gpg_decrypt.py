#!/usr/bin/env python3
import os
import sys

from argparse import ArgumentParser, RawTextHelpFormatter, FileType

def main(args):
    work_dir = args.work_dir
    files = os.listdir(work_dir)
    os.chdir(work_dir)

    for f in files:
        if not f.endswith('.gpg'):
            continue
        basename, ext = os.path.splitext(f)
        decrypt_file_path = os.path.join(work_dir, basename)
        if os.path.isfile(decrypt_file_path): # 已经解压了
            continue
        origin_file_path = os.path.join(work_dir, f)
        print(origin_file_path)
        os.system('/usr/bin/gpg --output {} {} --decrypt {}'.format(decrypt_file_path, " ".join(args.gpg_args), origin_file_path))

        # 然后将 mtime 保持一致
        file_stat_obj = os.stat(origin_file_path)
        if os.path.isfile(decrypt_file_path): # sometimes decripty failed
            os.utime(decrypt_file_path, (file_stat_obj.st_atime, file_stat_obj.st_mtime))

if __name__ == "__main__":
    parser = ArgumentParser(description='gpg decrypt file', formatter_class=RawTextHelpFormatter)
    parser.add_argument('work_dir', type=str, help='work dir')
    parser.add_argument('gpg_args', nargs='*', help='other args pass to gpg command')
    args = parser.parse_args()
    main(args)
