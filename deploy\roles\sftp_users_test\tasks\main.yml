---
- name: "检查用户{{ftp_user}}是否已存在"
  shell: getent passwd |grep "{{ftp_user}}"
  register: user_exsits
  failed_when: "user_exsits.rc == 2"

- name: shell return values
  debug:
    var: user_exsits.stdout

- name: "检查用户{{ftp_user}}是否已存在"
  fail:
    msg: "[Error],  {{ftp_user}} 用户已存在！"
  when: user_exsits.stdout != "" and  to_modify == "false"

- name: "检查目录{{ftp_bankname}}是否已存在"
  ansible.builtin.stat:
    path: "/data/banks/uploads/{{ftp_bankname}}"
  register: dir_exists
  changed_when: false
  tags: dir_checks

- name: "显示目录{{ftp_bankname}}状态"
  ansible.builtin.debug:
    var: dir_exists.stat.exists
  run_once: true
  changed_when: false

- name: "检查目录{{ftp_bankname}}状态"
  fail:
    msg: "[<PERSON>rror], 目录 /data/banks/uploads/{{ftp_bankname}} 已存在！"
  when: 
    - dir_exists.stat.exists
    - to_modify == "false"

- name: "create ftp user {{ftp_user}} home {{ftp_bankname}}"
  file:
    path: "/data/banks/uploads/{{ftp_bankname}}"
    state: directory
    mode: '0755'

- name: "create ftpuser {{ftp_user}} group"
  group:
    name: "{{ftp_user}}"
    state: present

- name: "create ftpuser {{ftp_user}} user"
  user:
    name: "{{ftp_user}}"
    shell: "/sbin/nologin"
    group: "{{ftp_user}}"
    createhome: true
    home: "/data/banks/uploads/{{ftp_bankname}}"
    password: "{{ ftpuser_test_passwd[ftp_user] }}"

#- name: print message from template
#  debug: msg="{{ lookup('template', 'keys/{{ftp_user}}/key.pub.j2')}}"
#  register: user_keys
#  ignore_errors: true

- name: "ensure ftpuser {{ftp_user}} home"
  file:
    path: "/data/banks/uploads/{{ftp_bankname}}"
    state: directory
    mode: '0755'
    owner: root
    group: "{{ftp_user}}"

- name: "ensure ftpuser {{ftp_user}} bank sub diretory {{ftp_bankname_sub}}"
  file:
    path: "/data/banks/uploads/{{ftp_bankname}}/{{ftp_bankname_sub}}"
    state: directory
    mode: '0755'
    owner: "{{ftp_user}}"
    group: "{{ftp_user}}"

- name: add ssh pub keys
  authorized_key:
    user: "{{ftp_user}}"
#    key: "{{ lookup('template', 'keys/{{ftp_user}}/key.pub.j2')}}"
    key: "{{ ftpuser_test_pub[ftp_user] }}"
    state: present
  when: ftpuser_test_pub[ftp_user] is defined
#  when: user_keys.msg != ""
#  ignore_errors: true

- name: modify sshd 
  ansible.builtin.blockinfile:
    block: |
                Match User     {{ftp_user}}
                ChrootDirectory %h
                X11Forwarding no
                AllowTcpForwarding no
                ForceCommand internal-sftp -l INFO -f local5
    path: /etc/ssh/sshd_config
    marker: "# {mark} ANSIBLE MANAGED BLOCK {{ ftp_user }}"
    state: present
    backup: yes
    validate: sshd -t -f %s
  notify:
  - reload ssh

- name: notify to wechat 
  shell: |
    content="
        ><font color='blue'>测试环境</font>
        >Server name/Host: <font  color='blue'>{{ hostvars[inventory_hostname]['ansible_host'] }}</font>
        >Port: <font  color='warning'>63100</font>
        >Folder Directory: <font  color='warning'>{{ ftp_bankname }}</font>
        >Login: <font  color='warning'>{{ ftp_user }}</font>
        >PWD: <font  color='warning'>{{ ftpuser_test_passwd[ ftp_user + '_plain' ] }}</font>
    
    "
    webHookUrl="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={{ wx_webhookkey }}"
    content='{"msgtype": "markdown","markdown": {"content": "'$content'","mentioned_list":"@all"},}'
    curl --data-ascii "$content" $webHookUrl

#- name: "查看 sshd 配置文件"
#  shell: cat /etc/ssh/sshd_config
#  register: sshd_config_content
#
#- name: shell return values
#  debug:
#    var: sshd_config_content.stdout_lines
######################################
#- name: absent ftpuser 
#  user:
#    name: "{{item}}"
#    state: absent
#  with_items:
#  - adts_test
#  - adts_test2
#  - adts_test3
#  - adts_test4
#  - adts_test5
#  - adts_test6
#- name: absent ftpuser group
#  group:
#    name: "{{item}}"
#    state: absent
#  with_items:
#  - adts_test
#  - adts_test2
#  - adts_test3
#  - adts_test4
#  - adts_test5
#  - adts_test6
#- name: absent modify sshd config
#  ansible.builtin.blockinfile:
#    block: |
#                Match User     "{{item}}"
#                ChrootDirectory %h
#                X11Forwarding no
#                AllowTcpForwarding no
#                ForceCommand internal-sftp -l INFO -f local5
#    path: /etc/ssh/sshd_config
#    marker: "# {mark} ANSIBLE MANAGED BLOCK {{ item }}"
#    state: absent
#    validate: sshd -t -f %s
#  loop:
#    - adts_test
#    - adts_test2
#    - adts_test3
#    - adts_test4
#    - adts_test5
#    - adts_test6
#  notify:
#  - reload ssh